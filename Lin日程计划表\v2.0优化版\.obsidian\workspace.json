{"main": {"id": "86b220b9c155ccbc", "type": "split", "children": [{"id": "afa0241f36a56e1e", "type": "tabs", "children": [{"id": "e3272ecd75ea303c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "03-生态系统架构层/系统解释文档/01-心矩生态系统目的解释-优化版.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "01-心矩生态系统目的解释-优化版"}}]}], "direction": "vertical"}, "left": {"id": "40566c575f7b5c27", "type": "split", "children": [{"id": "85c28abdddcd1afa", "type": "tabs", "children": [{"id": "0d03f3bb77118425", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "733494c9a2c8bedf", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "077c880d6e4c1196", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "f2de600e74de1f01", "type": "split", "children": [{"id": "1782e32a5ad0cc00", "type": "tabs", "dimension": 25, "children": [{"id": "c8ba82df5b2da2e0", "type": "leaf", "state": {"type": "backlink", "state": {"file": "v2.0优化版/01-人工记录输入层/记录界面/日记/2025/2025-07-16.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "2025-07-16 的反向链接列表"}}, {"id": "b85c34f619f87aef", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "v2.0优化版/01-人工记录输入层/记录界面/日记/README.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "README 的出链列表"}}, {"id": "971f6247a9deb570", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "42196ed70a55d249", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "calendar-with-checkmark", "title": "Calendar"}}, {"id": "974eb62ffc6cd911", "type": "leaf", "state": {"type": "custom-frames-💰-财务记录", "state": {}, "icon": "lucide-wallet", "title": "💰 财务记录"}}, {"id": "0ce7bd2c26918476", "type": "leaf", "state": {"type": "graph-analysis", "state": {}, "icon": "GA-ICON", "title": "Graph Analysis"}}], "currentTab": 5}, {"id": "032fe951a40762f3", "type": "tabs", "dimension": 25, "children": [{"id": "b943e01ed54d1b2f", "type": "leaf", "state": {"type": "graph", "state": {}, "icon": "lucide-git-fork", "title": "关系图谱"}}]}, {"id": "79ba825213cf9649", "type": "tabs", "dimension": 50, "children": [{"id": "14e20999a8071674", "type": "leaf", "state": {"type": "mermaid-toolbar-view", "state": {}, "icon": "trident-custom", "title": "Mermaid Toolbar"}}, {"id": "bf4a7760684a5474", "type": "leaf", "state": {"type": "outline", "state": {"file": "03-生态系统架构层/系统解释文档/01-心矩生态系统目的解释-优化版.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "01-心矩生态系统目的解释-优化版 的大纲"}}], "currentTab": 1}], "direction": "horizontal", "width": 391.5}, "left-ribbon": {"hiddenItems": {"graph:查看关系图谱": false, "cmdr:� 今日收入": false, "cmdr:📅 补录支出": false, "switcher:打开快速切换": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "daily-notes-editor:Open Daily Note Editor": false, "templater-obsidian:Templater": false, "obsidian42-brat:BRAT": false, "cmdr:📈 补录收入": false, "obsidian-kanban:创建新看板": false, "cmdr:⚡ 一键财务记录": false, "cmdr:💸 今日支出": false, "workspaces:管理工作区布局": false, "mermaid-tools:Open Mermaid Toolbar": false, "periodic-notes:Open this week": false}}, "active": "e3272ecd75ea303c", "lastOpenFiles": ["03-生态系统架构层/系统解释文档/02-生态系统形象描述-森林版.md", "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-27.md", "02-AI协作处理层/经验/03-插件配置实战/Graph Analysis插件深度理解.md", "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-25.md", "03-生态系统架构层/子系统生态/目标追踪生态系统/README.md", "02-AI协作处理层/经验/02-Obsidian软件理解/Obsidian软件理解说明.md", "02-AI协作处理层/AI协作处理的记忆库/模块记忆处理库.md", "02-AI协作处理层/AI协作处理的记忆库/模块记忆处理库", "02-AI协作处理层/AI协作处理的记忆库", "03-生态系统架构层/系统解释文档/01-心矩生态系统目的解释-优化版.md", "03-生态系统架构层/系统解释文档/02-生态系统形象描述.md", "03-生态系统架构层/系统解释文档/生态协同原理.md", "03-生态系统架构层/系统解释文档/01-心矩生态系统目的解释.md", "03-生态系统架构层/系统解释文档/README.md", "03-生态系统架构层/系统解释文档/01-主生态系统目的目标解释-v2.0.md", "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-26.md", "02-AI协作处理层/数据存储池/财务数据/财务仪表板-模块一-干净版.md", "03-生态系统架构层/系统解释文档/02-生态系统运作的诗意画卷-AI深度理解指南.md", "03-生态系统架构层/系统解释文档/02-AI深度理解的情境解析.md", "03-生态系统架构层/系统解释文档/02-梦境般的使用情境艺术画卷.md", "03-生态系统架构层/01-主生态系统目的目标解释.md", "03-生态系统架构层/系统解释文档/03-游戏化人生成长机制解释.md", "03-生态系统架构层/系统解释文档/01-主生态系统目的目标解释.md", "03-生态系统架构层/系统解释文档/02-AI协作管控人生理念解释.md", "03-生态系统架构层/系统解释文档", "02-AI协作处理层/数据存储池/财务数据/财务仪表板-模块二-纯粹计算版.md", "01-人工记录输入层/Obsidian模板库/时间节点模板.md", "02-AI协作处理层/数据存储池/财务数据/预算分配配置文档.md", "03-生态系统架构层/子系统生态/目标追踪生态系统/每日必做清单.md", "03-生态系统架构层/子系统生态/目标追踪生态系统", "03-生态系统架构层/子系统生态", "03-生态系统架构层/主生态管理器", "03-生态系统架构层", "02-AI协作处理层/数据存储池/财务数据/财务系统结构/插件配置指南", "READMEmd", "02-AI协作处理层/数据存储池/财务数据/财务系统结构"]}