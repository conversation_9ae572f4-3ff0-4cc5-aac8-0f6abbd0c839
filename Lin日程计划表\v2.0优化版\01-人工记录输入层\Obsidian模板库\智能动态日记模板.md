---
date: {{date:YYYY-MM-DD}}
display_date: {{date:YYYY年MM月DD日 dddd}}
created: {{date}}
week: {{date:w}}
weekday: {{date:d}}
tags: [日记, {{date:YYYY}}, {{date:MM月}}]
---

# 📅 {{date:YYYY年MM月DD日}} - {{date:dddd}} - 第{{date:w}}周

## 🎯 今日三件事
> [!tip] 💡 任务来源
> 从 [[总目标清单]] 和 [[每日必做清单]] 中选择今日最重要的3项任务

1. ____________________
2. ____________________
3. ____________________

---

## 📋 今日目标提醒（四象限法则）

> [!tip] 💡 重要性-紧急性四象限
> **优先级排序，时刻提醒自己专注重要的事情：**
>
> | 🔥 **重要且紧急** | 📋 **重要不紧急** |
> |------------------|------------------|
> | 立即处理的危机事件 | 计划、预防、能力建设 |
> | 截止日期压力的项目 | 人际关系、新机会 |
> | 紧急问题解决 | 休息、娱乐 |
>
> | ⚡ **紧急不重要** | 🗑️ **不重要不紧急** |
> |------------------|------------------|
> | 打断、电话、邮件 | 琐事、娱乐过度 |
> | 某些会议、活动 | 无意义的网络浏览 |
> | 紧急但可委托的事 | 时间浪费活动 |

**🎯 今日重点关注（重要不紧急象限）**：
1. ____________________
2. ____________________
3. ____________________

## 📝 今日记录
*想写什么就写什么*

---

## 🏃 今日运动安排

<%*
// 获取文件日期的星期数（而不是当前日期）
// 从文件名或frontmatter中获取日期
let dayNum;
try {
    // 尝试从文件名获取日期（格式：YYYY-MM-DD）
    const fileName = tp.file.title;
    const dateMatch = fileName.match(/(\d{4}-\d{2}-\d{2})/);
    if (dateMatch) {
        const fileDate = new Date(dateMatch[1]);
        dayNum = fileDate.getDay(); // 0=周日, 1=周一, 2=周二...
    } else {
        // 如果文件名中没有日期，使用当前日期作为后备
        dayNum = parseInt(tp.date.now("d"));
    }
} catch (error) {
    // 出错时使用当前日期
    dayNum = parseInt(tp.date.now("d"));
}

if (dayNum === 1) { // 周一 - 胸部和背部训练
    tR += `### 💪 上肢力量训练（周一专属）
**今日运动**：胸部 + 背部训练
**目标时长**：30分钟
**训练内容**：
- [ ] 热身（5分钟） #exercise
- [ ] 胸部训练：俯卧撑/哑铃推胸（10分钟） #exercise
- [ ] 背部训练：引体向上/哑铃划船（10分钟） #exercise
- [ ] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 胸部训练组数：____组
- 背部训练组数：____组
- 完成质量：很好👍 / 一般👌 / 不佳👎
- 训练感受：轻松😊 / 适中😐 / 困难😓`;

} else if (dayNum === 3) { // 周三 - 肩膀和腹部训练
    tR += `### 💪 核心力量训练（周三专属）
**今日运动**：肩膀 + 腹部训练
**目标时长**：30分钟
**训练内容**：
- [ ] 热身（5分钟） #exercise
- [ ] 肩膀训练：哑铃推举/侧平举（10分钟） #exercise
- [ ] 腹部训练：卷腹/平板支撑（10分钟） #exercise
- [ ] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 肩膀训练组数：____组
- 腹部训练时长：____分钟
- 完成质量：很好👍 / 一般👌 / 不佳👎
- 训练感受：轻松😊 / 适中😐 / 困难😓`;

} else if (dayNum === 5) { // 周五 - 腿部训练
    tR += `### 💪 下肢力量训练（周五专属）
**今日运动**：腿部训练
**目标时长**：30分钟
**训练内容**：
- [ ] 热身（5分钟） #exercise
- [ ] 腿部训练：深蹲/弓步蹲（15分钟） #exercise
- [ ] 小腿训练：提踵/跳跃（5分钟） #exercise
- [ ] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 深蹲组数：____组
- 弓步蹲组数：____组
- 完成质量：很好👍 / 一般👌 / 不佳👎
- 训练感受：轻松😊 / 适中😐 / 困难😓`;

} else if (dayNum === 2 || dayNum === 4) { // 周二四
    tR += `### 🚶 走路运动（周二四专属）
**今日运动**：走路1万步
**目标时长**：60分钟
**运动内容**：
- [ ] 走路1万步 #exercise
- [ ] 路线选择：____ #exercise
- [ ] 拉伸放松（10分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 实际步数：____步
- 运动感受：很爽😄 / 还行😊 / 累😴
- 天气影响：无影响☀️ / 有影响🌧️`;

} else if (dayNum === 6) { // 周六
    tR += `### 🎯 自由运动（周六专属）
**今日运动**：自选运动
**运动内容**：
- [ ] 运动类型：____ #exercise
- [ ] 运动时长：____分钟 #exercise
- [ ] 运动地点：____ #exercise

**实际完成**：
- 运动选择原因：____
- 运动感受：____`;

} else { // 周日
    tR += `### 😌 休息日（周日专属）
**今日安排**：休息恢复
**轻度活动**：
- [ ] 散步（____分钟） #exercise
- [ ] 拉伸（____分钟） #exercise
- [ ] 完全休息 #exercise

**身体状况**：
- 身体恢复情况：很好💪 / 一般😐 / 需要更多休息😴`;
}
%>

---

## 📊 今日数据录入

> [!info] 🎯 阅读模式交互输入
> **安装Meta Bind插件后，可在阅读模式下直接输入数据：**
> - 数字输入框：直接点击输入数字
> - 滑块：拖动选择数值
> - 数据自动保存到文件
>
> **插件安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 😴 睡眠
- [ ] 睡眠时长记录：`INPUT[number:sleep]`小时 #data

### 🚶 步数
- [ ] 步数记录：`INPUT[number:steps]`步 #data

### 💼 工作学习
- [ ] 工作时长记录：`INPUT[number:work_hours]`小时 #work
- [ ] 学习时长记录：`INPUT[number:study_hours]`小时 #study
- [ ] 工作任务完成 #work
- [ ] 学习目标达成 #study

### 😊 心情
- [ ] 心情评分：`INPUT[slider:mood]` (1-10分) #data
- [ ] 心情记录完成 #data

---

## 📈 智能进度条系统

> [!tip] 💡 进度条更新方法
> **阅读模式下勾选任务后，进度条需要手动刷新：**
> - 方法1：按 **Ctrl+R** (Windows) 或 **Cmd+R** (Mac) 刷新页面
> - 方法2：切换到编辑模式再切回阅读模式
> - 方法3：按 **F5** 刷新整个页面
>
> **推荐**：勾选完所有任务后统一刷新一次即可

### 🎯 主要任务状态

```dataviewjs
// 智能主任务状态显示
const tasks = dv.current().file.tasks || [];

// 获取各类子任务
const exerciseTasks = tasks.filter(t => t.text.includes('#exercise'));
const workTasks = tasks.filter(t => t.text.includes('#work'));
const studyTasks = tasks.filter(t => t.text.includes('#study'));
const dataTasks = tasks.filter(t => t.text.includes('#data'));

// 计算完成状态
function getTaskStatus(subTasks, taskName) {
    if (subTasks.length === 0) return `- ⚪ ${taskName} (暂无子任务)`;

    const completed = subTasks.filter(t => t.completed).length;
    const total = subTasks.length;
    const percentage = Math.round((completed / total) * 100);

    let statusIcon, statusText;
    if (percentage === 100) {
        statusIcon = "✅"; statusText = "已完成";
    } else if (percentage >= 75) {
        statusIcon = "🎯"; statusText = "接近完成";
    } else if (percentage >= 50) {
        statusIcon = "💪"; statusText = "进行中";
    } else if (percentage >= 25) {
        statusIcon = "🚀"; statusText = "已开始";
    } else {
        statusIcon = "⏰"; statusText = "待开始";
    }

    return `- ${statusIcon} ${taskName} ${statusText} (${completed}/${total})`;
}

// 获取文件日期对应的运动任务名称
const fileDate = new Date(dv.current().date);
const dayNum = fileDate.getDay();
let exerciseTaskName = "";
if (dayNum === 1) exerciseTaskName = "胸部+背部训练";
else if (dayNum === 3) exerciseTaskName = "肩膀+腹部训练";
else if (dayNum === 5) exerciseTaskName = "腿部训练";
else if (dayNum === 2 || dayNum === 4) exerciseTaskName = "走路1万步";
else if (dayNum === 6) exerciseTaskName = "自由运动";
else exerciseTaskName = "休息恢复";

// 显示智能主任务状态
dv.paragraph(getTaskStatus(exerciseTasks, exerciseTaskName));
dv.paragraph(getTaskStatus(workTasks, "工作任务"));
dv.paragraph(getTaskStatus(studyTasks, "学习目标"));
dv.paragraph(getTaskStatus(dataTasks, "数据记录"));
```

### 📊 详细进度统计

```dataviewjs
// 获取当前页面的所有任务
const tasks = dv.current().file.tasks || [];

// 创建智能进度条函数
function createProgressBar(completed, total) {
    if (total === 0) return "⚪ 暂无任务";
    
    const percentage = Math.round((completed / total) * 100);
    const width = 10;
    const filledBars = Math.floor((percentage / 100) * width);
    const emptyBars = width - filledBars;
    
    let fillChar, emptyChar, statusIcon;
    
    if (percentage === 100) {
        fillChar = "🟢"; emptyChar = "⚪"; statusIcon = "✅";
    } else if (percentage >= 75) {
        fillChar = "🟡"; emptyChar = "⚪"; statusIcon = "🎯";
    } else if (percentage >= 50) {
        fillChar = "🔵"; emptyChar = "⚪"; statusIcon = "💪";
    } else if (percentage >= 25) {
        fillChar = "🟠"; emptyChar = "⚪"; statusIcon = "🚀";
    } else {
        fillChar = "🔴"; emptyChar = "⚪"; statusIcon = "⏰";
    }
    
    const progressBar = fillChar.repeat(Math.max(1, filledBars)) + emptyChar.repeat(emptyBars);
    return `${statusIcon} ${progressBar} **${percentage}%** (${completed}/${total})`;
}

// 获取智能建议
function getSuggestion(completed, total, category) {
    const percentage = Math.round((completed / total) * 100);
    const remaining = total - completed;
    
    if (percentage === 100) {
        return `🎉 ${category}全部完成！`;
    } else if (percentage >= 75) {
        return `👍 ${category}接近完成，还剩${remaining}项`;
    } else if (percentage >= 50) {
        return `💪 ${category}过半完成，继续加油`;
    } else if (percentage >= 25) {
        return `🚀 ${category}已开始，还需努力`;
    } else {
        return `⏰ ${category}需要开始行动了`;
    }
}

// 第二层：分类任务进度
dv.header(4, "📋 分类任务进度");

const categories = [
    { 
        name: "🏃 运动任务", 
        mainTag: "#task-exercise",
        subTag: "#exercise",
        mainTasks: tasks.filter(t => t.text.includes('#task-exercise')),
        subTasks: tasks.filter(t => t.text.includes('#exercise'))
    },
    { 
        name: "💼 工作任务", 
        mainTag: "#task-work",
        subTag: "#work",
        mainTasks: tasks.filter(t => t.text.includes('#task-work')),
        subTasks: tasks.filter(t => t.text.includes('#work'))
    },
    { 
        name: "📚 学习任务", 
        mainTag: "#task-study",
        subTag: "#study",
        mainTasks: tasks.filter(t => t.text.includes('#task-study')),
        subTasks: tasks.filter(t => t.text.includes('#study'))
    },
    { 
        name: "📊 数据记录", 
        mainTag: "#task-data",
        subTag: "#data",
        mainTasks: tasks.filter(t => t.text.includes('#task-data')),
        subTasks: tasks.filter(t => t.text.includes('#data'))
    }
];

categories.forEach(cat => {
    if (cat.subTasks.length > 0) {
        const subCompleted = cat.subTasks.filter(t => t.completed).length;
        const subTotal = cat.subTasks.length;
        const mainCompleted = cat.mainTasks.filter(t => t.completed).length;
        const mainTotal = cat.mainTasks.length;
        
        dv.paragraph(`**${cat.name}**:`);
        dv.paragraph(`└─ 子任务: ${createProgressBar(subCompleted, subTotal)}`);
        if (mainTotal > 0) {
            dv.paragraph(`└─ 主任务: ${createProgressBar(mainCompleted, mainTotal)}`);
        }
        dv.paragraph(`💡 ${getSuggestion(subCompleted, subTotal, cat.name.split(' ')[1])}`);
        dv.paragraph("");
    }
});

// 第三层：今日总进度
dv.header(4, "🎯 今日总进度统计");

const allMainTasks = tasks.filter(t => t.text.includes('#task-'));
const allSubTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allMainTasks.length > 0 || allSubTasks.length > 0) {
    const mainCompleted = allMainTasks.filter(t => t.completed).length;
    const mainTotal = allMainTasks.length;
    const subCompleted = allSubTasks.filter(t => t.completed).length;
    const subTotal = allSubTasks.length;
    
    // 计算综合完成度（主任务权重60%，子任务权重40%）
    let overallPercentage = 0;
    if (mainTotal > 0 && subTotal > 0) {
        const mainPercentage = (mainCompleted / mainTotal) * 100;
        const subPercentage = (subCompleted / subTotal) * 100;
        overallPercentage = Math.round(mainPercentage * 0.6 + subPercentage * 0.4);
    } else if (mainTotal > 0) {
        overallPercentage = Math.round((mainCompleted / mainTotal) * 100);
    } else if (subTotal > 0) {
        overallPercentage = Math.round((subCompleted / subTotal) * 100);
    }
    
    dv.paragraph(`**🌟 今日综合完成度**: ${createProgressBar(overallPercentage, 100).replace(/\(\d+\/\d+\)/, `(综合评分: ${overallPercentage}%)`)}`);
    dv.paragraph(`├─ 主要任务: ${createProgressBar(mainCompleted, mainTotal)}`);
    dv.paragraph(`└─ 详细任务: ${createProgressBar(subCompleted, subTotal)}`);
    
    // 综合建议
    let overallSuggestion = "";
    if (overallPercentage >= 90) {
        overallSuggestion = "🏆 今天表现卓越！所有任务都完成得很好！";
    } else if (overallPercentage >= 75) {
        overallSuggestion = "🎯 今天进展很好！再坚持一下就能完美收官！";
    } else if (overallPercentage >= 50) {
        overallSuggestion = "💪 今天过半完成！保持节奏，继续加油！";
    } else if (overallPercentage >= 25) {
        overallSuggestion = "🚀 今天已经开始！建议重点关注未完成的重要任务！";
    } else {
        overallSuggestion = "⏰ 今天需要加速了！建议从最重要的任务开始！";
    }
    
    dv.paragraph(`🎪 **综合建议**: ${overallSuggestion}`);
    
} else {
    dv.paragraph("⚪ 暂无任务，开始勾选上面的任务吧！");
}
```

---

## 🧠 情绪感受记录 {-}

### 📝 今日情绪事件（有什么触发情绪的事就简单记一下）

> [!tip]- 💡 情绪事件记录使用说明
> **使用流程**：
> 1. **用户只需填写详细记录** - 想写什么就写什么，把内心想法倒出来
> 2. **AI提取四个维度** - 从详细记录中分析提取脑子想的、身体感受等
> 3. **AI记录操作逻辑** - 说明分析步骤和逻辑依据，便于理解和改进
>
> **标注块说明**：
> - `[!quote]` 灰色 - 用户原创记录
> - `[!abstract]` 青色 - AI提取的四个维度
> - `[!info]` 蓝色 - AI操作指引与逻辑链
>
> 详细使用方法请参考：[[Obsidian标注块(Callout)使用指南]]

#### **事件1**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：*想写什么就写什么，把内心的想法都倒出来...*

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件2**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件3**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件4**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件5**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件6**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

> [!tip] 复杂情绪识别
> 如果遇到难以名状的复杂情绪，可以使用专门的模板：
> **[[情绪识别与调节权威清单]]** - 基于权威心理学理论的情绪识别与调节策略
>
> *需要更多事件记录时，直接复制粘贴上面事件格式即可*

### 🎭 今日主要情绪（AI来勾选）
- [ ] 😊 开心 #emotion
- [ ] 😔 难过 #emotion
- [ ] 😰 焦虑 #emotion
- [ ] 😠 生气 #emotion
- [ ] 😴 疲惫 #emotion
- [ ] 😌 平静 #emotion
- [ ] 🤔 困惑 #emotion
- [ ] 😤 烦躁 #emotion

### 💭 今日感受总结（AI来完成）
*AI会根据上面的详细记录来分析和总结情绪状态*

---

---

## 🤖 AI分析
*AI会根据上面的数据自动分析*

**运动数据**：
**时间分配**：
**效率评估**：
**建议优化**：

---

## 💰 财务概览

> [!tip] 💡 使用右侧边栏快速记录
> 点击右侧边栏的"💸 快速支出"或"💰 快速收入"按钮进行记录

### 📊 今日财务数据

**💰 收入总计**：____元 | **💸 支出总计**：____元 | **💵 结余**：____元

### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|

### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|

### 📊 今日财务总结

**💰 今日收入总计**：____元
**💸 今日支出总计**：____元
**💵 今日结余**：____元

**📋 支出分类统计**：
- 🍽️ 餐饮：____元 | 🚗 交通：____元 | 🛍️ 购物：____元 | 🎮 娱乐：____元
- 📚 学习：____元 | 🏥 医疗：____元 | 🏠 房租：____元 | 💡 水电：____元
- 📱 通讯：____元 | 📦 快递：____元 | 💄 美容：____元 | 👕 服装：____元
- 🧴 日用品：____元 | 🎁 礼品：____元 | 🚕 打车：____元 | ☕ 咖啡：____元
- 🍎 零食：____元 | 💊 药品：____元 | 🔧 维修：____元 | 🔄 其他：____元

**💭 财务反思**：
- 今日最大支出：____________________
- 是否有冲动消费：____________________
- 明日消费计划：____________________

---

**完成时间**：{{time}}
