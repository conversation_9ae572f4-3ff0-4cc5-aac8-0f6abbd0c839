# 📋 每日必做清单

> [!tip] 💡 使用说明
> 这是每天都需要完成的固定任务清单，在日记的"今日三件事"中选择其中最重要的3项

## 🌅 晨间例行 (7:00-9:00)

### 📝 规划类
- [ ] 查看[[总目标清单]]，确定今日重点目标
- [ ] 在日记中填写"今日三件事"
- [ ] 检查四象限，明确优先级

### 💰 财务类
- [ ] 查看[[财务状态面板]]，了解当前余额
- [ ] 检查昨日支出记录是否完整
- [ ] 确认今日预算安排

### 🌟 生态系统检查
- [ ] 检查各子系统运行状态
- [ ] 查看系统间协作提醒
- [ ] 确认今日数据收集重点

## 🌞 日间执行 (9:00-18:00)

### 🎯 目标推进
- [ ] 专注完成"今日三件事"中的第1项
- [ ] 推进1个重要不紧急的长期目标
- [ ] 及时记录任务完成状态

### 💸 支出管理
- [ ] 每次消费后立即记录支出
- [ ] 消费前检查预算余额
- [ ] 控制冲动消费，遵循必要性原则

### 🏃 健康维护
- [ ] 完成当日运动计划（根据星期安排）
- [ ] 保持适量饮水
- [ ] 注意用眼休息

### 🤝 系统协作
- [ ] 记录跨系统的数据关联
- [ ] 更新系统间的状态同步
- [ ] 处理系统协作提醒

## 🌙 晚间总结 (18:00-22:00)

### 📊 数据记录
- [ ] 完成日记中的情绪记录
- [ ] 更新运动数据和步数
- [ ] 记录睡眠时间和质量

### 🎯 目标回顾
- [ ] 检查今日三件事完成情况
- [ ] 在[[总目标清单]]中更新进度
- [ ] 规划明日重点任务

### 💰 财务总结
- [ ] 检查今日支出总计
- [ ] 更新财务状态面板
- [ ] 分析支出合理性

### 🌟 生态系统维护
- [ ] 检查各系统数据完整性
- [ ] 更新系统协作状态
- [ ] 记录系统优化建议

## 🔄 周期性任务

### 📅 每周必做 (周日)
- [ ] 回顾本周目标完成情况
- [ ] 更新[[总目标清单]]中的任务状态
- [ ] 制定下周重点目标
- [ ] 清理已完成的任务
- [ ] 评估生态系统协作效果

### 📅 每月必做 (月末)
- [ ] 月度财务总结和分析
- [ ] 月度目标达成率统计
- [ ] 调整下月预算和目标
- [ ] 归档已完成的重要目标
- [ ] 生态系统性能评估和优化

## 🎯 优先级指南

### 🔴 高优先级 (必须完成)
1. 财务支出记录
2. 日记情绪记录
3. 今日三件事中的第1项
4. 系统数据完整性检查

### 🟡 中优先级 (尽量完成)
1. 运动计划执行
2. 目标进度更新
3. 数据记录完善
4. 系统协作状态更新

### 🟢 低优先级 (有时间再做)
1. 系统优化调整
2. 额外学习任务
3. 非必要的整理工作
4. 生态系统功能扩展

## 📊 完成度追踪

```dataviewjs
// 显示今日必做清单的完成情况
const today = new Date().toISOString().split('T')[0];

// 尝试获取今日日记文件
const todayDiary = dv.pages('"Lin日程计划表/v2.0优化版/01-人工记录输入层/记录界面/日记"')
    .where(p => p.file.name.includes(today))
    .first();

if (todayDiary) {
    const content = await dv.io.load(todayDiary.file.path);
    
    // 统计今日三件事的完成情况
    const threeThingsMatches = content.match(/## 🎯 今日三件事[\s\S]*?(?=##|$)/);
    
    if (threeThingsMatches) {
        const threeThingsContent = threeThingsMatches[0];
        const taskMatches = threeThingsContent.match(/\d+\.\s*(.+)/g);
        
        if (taskMatches) {
            dv.header(4, "📅 今日三件事完成情况");
            
            let completedCount = 0;
            let taskData = [["序号", "任务", "状态"]];
            
            for (let i = 0; i < taskMatches.length; i++) {
                const task = taskMatches[i];
                let status = "⚪ 待完成";
                
                if (task.includes("✅") || task.includes("[x]")) {
                    status = "✅ 已完成";
                    completedCount++;
                } else if (task.includes("🟡") || task.includes("[/]")) {
                    status = "🟡 进行中";
                }
                
                const taskText = task.replace(/\d+\.\s*/, '').replace(/[✅🟡⚪\[\]x\/]/g, '').trim();
                taskData.push([`${i+1}`, taskText, status]);
            }
            
            dv.table(taskData);
            
            const completionRate = (completedCount / taskMatches.length * 100).toFixed(1);
            dv.paragraph(`**完成率**: ${completionRate}% (${completedCount}/${taskMatches.length})`);
            
            // 生态系统协作提醒
            if (completedCount > 0) {
                dv.paragraph("🌟 **生态系统提醒**: 已完成的任务数据将自动同步到相关子系统");
            }
        }
    }
} else {
    dv.paragraph("📝 今日日记尚未创建，请先创建今日日记");
}

// 显示使用建议
dv.header(4, "💡 使用建议");
dv.paragraph("1. 每天早上查看此清单，选择3个最重要的任务");
dv.paragraph("2. 在日记的'今日三件事'中引用具体任务");
dv.paragraph("3. 完成任务后及时更新状态");
dv.paragraph("4. 晚上回顾完成情况，为明日做准备");
dv.paragraph("5. 关注任务间的生态系统协作关系");
```

## 🌟 生态系统协作示例

### 🤝 任务协作流程
```
晨间任务: "查看财务状态面板"
    ↓
系统协作:
├─ 💰 财务系统: 提供当前余额和支出统计
├─ 🎯 目标系统: 显示财务相关目标进度
└─ 📊 数据系统: 生成财务趋势分析
    ↓
决策支持:
└─ 基于多系统数据，制定今日消费计划
```

## 🔗 相关链接

- [[总目标清单]] - 查看所有目标和长期计划
- [[财务状态面板]] - 查看当前财务状况
- [[项目进度跟踪]] - 查看具体项目进展
- [[生态系统架构层]] - 查看系统架构设计

---

**💡 记住**: 这个清单是您每日行动的指南，选择其中最重要的任务放入日记的"今日三件事"中执行！

**🌟 生态系统版本**: v2.0
**📅 最后更新**: 2025-07-25
